const MangaContentDownloader = require('./download-manga-content');
const path = require('path');
const fs = require('fs-extra');

async function testBatchDownload() {
    const downloader = new MangaContentDownloader();
    
    try {
        console.log('🚀 初始化下载器...');
        await downloader.init();
        
        console.log('📋 测试批量下载功能');
        console.log('🔧 修复内容:');
        console.log('   - 移除章节数限制，下载所有可用章节');
        console.log('   - 增强错误处理和重试机制');
        console.log('   - 详细的下载进度和统计信息');
        console.log('   - 连续失败检测机制');
        console.log('');
        
        // 检查漫画列表文件
        const mangaListFile = path.join('./manga-ids.json');
        if (!await fs.pathExists(mangaListFile)) {
            console.log('❌ 未找到漫画列表文件，创建测试用的列表...');
            
            // 创建一个测试用的漫画列表
            const testMangaList = [
                { id: 'ap101511', name: '测试漫画1' },
                { id: 'ap101512', name: '测试漫画2' }  // 这个ID可能不存在，用于测试错误处理
            ];
            
            await fs.writeJson(mangaListFile, testMangaList, { spaces: 2 });
            console.log('✅ 已创建测试漫画列表');
        }
        
        // 记录开始时间
        const startTime = Date.now();
        
        // 测试批量下载（只下载前2个漫画进行测试）
        console.log('\n📚 开始测试批量下载...');
        await downloader.downloadFromMangaList(mangaListFile, 0, 2); // 下载前2个漫画，不限制章节数
        
        // 记录结束时间
        const endTime = Date.now();
        const duration = (endTime - startTime) / 1000;
        
        console.log(`\n✅ 批量下载测试完成！`);
        console.log(`⏱️ 总耗时: ${duration.toFixed(2)} 秒`);
        console.log('📊 请检查输出目录中是否包含了每个漫画的所有章节');
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
    } finally {
        await downloader.close();
    }
}

// 单独测试单个漫画的多章节下载
async function testSingleMangaMultipleChapters() {
    const downloader = new MangaContentDownloader();
    
    try {
        console.log('🚀 初始化下载器...');
        await downloader.init();
        
        console.log('📖 测试单个漫画多章节下载');
        
        const testMangaId = 'ap101511'; // 请替换为实际的漫画ID
        const testMangaName = '测试漫画';
        
        // 手动下载前几章进行测试
        for (let chapter = 1; chapter <= 5; chapter++) {
            console.log(`\n📚 测试下载第 ${chapter} 章...`);
            
            try {
                const success = await downloader.downloadMangaContent(testMangaId, testMangaName, chapter);
                
                if (success) {
                    console.log(`✅ 第 ${chapter} 章下载成功`);
                } else {
                    console.log(`📄 第 ${chapter} 章不存在，停止测试`);
                    break;
                }
            } catch (error) {
                console.error(`❌ 第 ${chapter} 章下载失败:`, error.message);
                if (error.message.includes('404')) {
                    console.log(`📄 第 ${chapter} 章确认不存在，停止测试`);
                    break;
                }
            }
            
            // 章节间延时
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
    } finally {
        await downloader.close();
    }
}

// 根据命令行参数选择测试类型
async function main() {
    const testType = process.argv[2] || 'batch';
    
    if (testType === 'single') {
        console.log('🧪 运行单个漫画多章节测试...\n');
        await testSingleMangaMultipleChapters();
    } else {
        console.log('🧪 运行批量下载测试...\n');
        await testBatchDownload();
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = { testBatchDownload, testSingleMangaMultipleChapters };
