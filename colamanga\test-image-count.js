const MangaContentDownloader = require('./download-manga-content');
const path = require('path');

async function testImageCountComparison() {
    const downloader = new MangaContentDownloader();
    
    try {
        console.log('🚀 初始化下载器...');
        await downloader.init();
        
        // 测试图片数量对比功能
        const testMangaId = 'ap101511'; // 请替换为实际的漫画ID
        const testChapter = 1;
        
        console.log(`📖 测试图片数量对比功能: 漫画ID ${testMangaId} 第${testChapter}章`);
        console.log('🔧 测试内容:');
        console.log('   - 智能滚动触发懒加载');
        console.log('   - 等待所有图片加载完成');
        console.log('   - 准确统计网页图片数量');
        console.log('   - 与本地文件数量对比');
        console.log('');
        
        // 访问章节页面
        const chapterUrl = `https://www.colamanga.com/manga-${testMangaId}/1/${testChapter}.html`;
        console.log(`🔗 访问章节: ${chapterUrl}`);
        
        await downloader.page.goto(chapterUrl, { waitUntil: 'domcontentloaded', timeout: 60000 });
        
        // 记录开始时间
        const startTime = Date.now();
        
        // 测试获取网页图片数量
        console.log('\n📊 开始测试网页图片数量获取...');
        const webImageCount = await downloader.getWebImageCount();
        
        // 记录结束时间
        const endTime = Date.now();
        const duration = (endTime - startTime) / 1000;
        
        console.log(`\n✅ 测试完成！`);
        console.log(`📊 网页图片数量: ${webImageCount} 张`);
        console.log(`⏱️ 耗时: ${duration.toFixed(2)} 秒`);
        
        // 如果图片数量大于0，说明检测成功
        if (webImageCount > 0) {
            console.log(`🎯 图片数量检测成功！现在应该能正确对比本地和网页的图片数量了。`);
            
            // 额外显示详细的加载状态
            const detailedStatus = await downloader.page.evaluate(() => {
                const comicPics = document.querySelectorAll('.mh_comicpic');
                const status = {
                    totalElements: comicPics.length,
                    withP: 0,
                    withBlob: 0,
                    samples: []
                };
                
                for (let i = 0; i < Math.min(comicPics.length, 5); i++) {
                    const pic = comicPics[i];
                    const img = pic.querySelector('img');
                    const pValue = pic.getAttribute('p');
                    
                    if (pValue) status.withP++;
                    if (img && img.src && img.src.startsWith('blob:')) status.withBlob++;
                    
                    status.samples.push({
                        index: i,
                        p: pValue,
                        hasImg: !!img,
                        src: img ? img.src.substring(0, 50) + '...' : 'no img'
                    });
                }
                
                return status;
            });
            
            console.log('\n📋 详细状态:');
            console.log(`   总元素: ${detailedStatus.totalElements}`);
            console.log(`   带p属性: ${detailedStatus.withP}`);
            console.log(`   已加载blob: ${detailedStatus.withBlob}`);
            console.log('   前5个元素样本:', detailedStatus.samples);
            
        } else {
            console.log(`⚠️ 未检测到图片，可能需要进一步调试。`);
        }
        
    } catch (error) {
        console.error('❌ 测试过程中出错:', error);
    } finally {
        await downloader.close();
    }
}

// 运行测试
if (require.main === module) {
    testImageCountComparison().catch(console.error);
}

module.exports = testImageCountComparison;
